﻿using Dapper;
using DocumentFormat.OpenXml.Wordprocessing;
using EngagetoDapper.Data.Connections;
using EngagetoEntities.Dtos.AutomationDtos;
using EngagetoEntities.Dtos.ContactDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoEntities.UserEntities.Dtos;
using Newtonsoft.Json;
using System.Data;
using System.Text;



namespace EngagetoDapper.Data.Dapper.Repositories.InboxRepositories
{
    public class InboxRepository : IInboxRepository
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ISqlConnectionFactory _connectionFactory;

        public InboxRepository(IUnitOfWork unitOfWork, ISqlConnectionFactory connectionFactory)
        {
            _unitOfWork = unitOfWork;
            _connectionFactory = connectionFactory;
        }

        public async Task<int> GetConversationCountAsync(Guid businessId, string role, Guid? userId = null)
        {
            var sql = @"SELECT COUNT(*)
                        FROM Contacts c
                        INNER JOIN (
                                    SELECT DISTINCT
                                    REPLACE(
                                            CASE
                                                WHEN [From] = @BusinessId THEN [To]
                                                WHEN [To] = @BusinessId THEN [From]
                                            END, '+', ''
                                           ) AS ContactNumber
                                   FROM Conversations
                                   WHERE [From] = @BusinessId OR [To] = @BusinessId
                                  ) conv
                      ON REPLACE(c.CountryCode + c.Contact, '+', '') = conv.ContactNumber
                      WHERE c.BusinessId = @BusinessId";

            if (role == "Admin" || role == "Owner")
            {
                sql += " AND c.IsActive = 1 AND c.IsDeleted = 0";
            }
            else if (userId.HasValue)
            {
                sql += " AND c.IsActive = 1 AND c.IsDeleted = 0 AND c.UserId = @UserId";
            }

            var totalFiltered = await _unitOfWork.Connection.QuerySingleAsync<int>(
                sql,
                new
                {
                    BusinessId = businessId.ToString().ToUpper(),
                    UserId = userId
                },
                _unitOfWork.Transaction ?? null);

            return totalFiltered;
        }

        public async Task<IEnumerable<dynamic>> GetPaginatedConversationsAsync(Guid businessId, CancellationToken cancellationToken)
        {
            if (cancellationToken.IsCancellationRequested) cancellationToken.ThrowIfCancellationRequested();

            var sqlQuery = @" WITH RankedMessages AS ( SELECT  REPLACE( 
                    CASE 
                        WHEN [From] = @BusinessId THEN [To]  
                        ELSE [From]  
                        END, '+', ''
                   ) AS CustomerNumber, Id,CreatedAt,
                   CASE 
                       WHEN TextMessage IS NOT NULL AND TextMessage != '' THEN TextMessage
                       WHEN MediaCaption IS NOT NULL AND MediaCaption != '' THEN MediaCaption
                       ELSE TemplateBody
                  END AS [Message],
                  ROW_NUMBER() OVER (
                  PARTITION BY REPLACE(
                    CASE 
                        WHEN [From] = @BusinessId THEN [To]  
                        ELSE [From]  
                    END, '+', ''
                )
                ORDER BY CreatedAt DESC
                ) AS rn
                FROM Conversations
                WHERE 
                ([From] = @BusinessId OR [To] = @BusinessId)
                )
                SELECT  CustomerNumber,Id, CreatedAt AS LatestCreatedAt, [Message]
                FROM RankedMessages
                WHERE rn = 1
                ORDER BY LatestCreatedAt DESC;";

            var param = new
            {
                BusinessId = businessId.ToString().ToUpper(),
            };

            return await _unitOfWork.Connection.QueryAsync<dynamic>(
                sqlQuery,
                param,
                _unitOfWork.Transaction ?? null);
        }

        public async Task<IEnumerable<EngagetoEntities.Entities.Contacts>> GetPaginatedContactsAsync(int page, int pageSize, Guid businessId, CancellationToken cancellationToken)
        {
            if (cancellationToken.IsCancellationRequested) cancellationToken.ThrowIfCancellationRequested();
            if (page < 1) page = 1;
            var offset = (page - 1) * pageSize;

            var sqlQuery = @"WITH UniqueContacts AS ( SELECT *, ROW_NUMBER() OVER (
                   PARTITION BY Contact 
                   ORDER BY 
                       CASE WHEN LastMessageAt IS NOT NULL THEN 0 ELSE 1 END, 
                       COALESCE(LastMessageAt, CreatedDate) DESC, 
                       CreatedDate DESC
               ) AS RowNum
               FROM Contacts
               WHERE IsActive = @IsActive AND BusinessId = @BusinessId)
               SELECT * FROM UniqueContacts
               WHERE RowNum = 1 
               ORDER BY 
                 CASE WHEN LastMessageAt IS NOT NULL THEN 0 ELSE 1 END, 
                 COALESCE(LastMessageAt, CreatedDate) DESC, 
                 CreatedDate DESC
               OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY";

            var param = new
            {
                IsActive = true,
                BusinessId = businessId,
                PageSize = pageSize,
                Offset = offset
            };

            return await _unitOfWork.Connection.QueryAsync<EngagetoEntities.Entities.Contacts>(
                    sqlQuery,
                    param,
                    _unitOfWork.Transaction ?? null);
        }

        public async Task<List<EngagetoEntities.Entities.Conversations>> GetLatestConversationsForContactsAsync(List<Contacts> contacts, CancellationToken cancellationToken)
        {
            if (cancellationToken.IsCancellationRequested) cancellationToken.ThrowIfCancellationRequested();
            if (contacts == null || !contacts.Any()) return new List<EngagetoEntities.Entities.Conversations>();

            var contactNumbers = contacts.SelectMany(c => new[] { c.Contact, c.CountryCode?.Replace("+", "") + c.Contact }).Distinct().ToList();

            var sqlQuery = @" WITH RankedConversations AS ( SELECT c.*, ROW_NUMBER() OVER (
                              PARTITION BY CASE 
                              WHEN c.[From] IN @ContactNumbers THEN c.[From] 
                              WHEN c.[To] IN @ContactNumbers THEN c.[To] 
                              ELSE NULL END 
                              ORDER BY c.CreatedAt DESC
                             ) AS RowNum
                            FROM Conversations c
                            WHERE c.[From] IN @ContactNumbers OR c.[To] IN @ContactNumbers )
                           SELECT * FROM RankedConversations WHERE RowNum = 1
                           ORDER BY CreatedAt DESC;";

            var param = new { ContactNumbers = contactNumbers };

            var conversations = await _unitOfWork.Connection.QueryAsync<EngagetoEntities.Entities.Conversations>(
                sqlQuery,
                param,
                _unitOfWork.Transaction);

            return conversations.ToList();
        }


        public async Task<IEnumerable<Contacts>> GetAllContactsAsync(List<Conversations> conversations, Guid businessId, string userId, CancellationToken cancellationToken)
        {

            var customerNumbers = conversations
                .Select(c => (c as dynamic)?.CustomerNumber)
                .Where(cn => !string.IsNullOrEmpty(cn))
                .ToList();

            if (customerNumbers.Count == 0) return new List<Contacts>();

            var roleQuery = @" SELECT r.Name 
                               FROM UserRole ur
                               JOIN Role r ON LOWER(ur.RoleId) = LOWER(r.Id)
                               WHERE LOWER(ur.Id) = LOWER(@UserId)";

            var role = await _unitOfWork.Connection.QueryFirstOrDefaultAsync<string>(roleQuery, new { UserId = userId }, _unitOfWork.Transaction);

            string contactQuery = role == "Admin" || role == "Owner"
                ? @"SELECT * 
                    FROM Contacts c
                    WHERE LOWER(c.BusinessId) = LOWER(@BusinessId) 
                    AND c.IsActive = 1
                    AND c.PhoneNumber IN @CustomerNumbers"
                : @"SELECT * 
                    FROM Contacts c
                    WHERE LOWER(c.BusinessId) = LOWER(@BusinessId) 
                    AND c.IsActive = 1 
                    AND LOWER(c.UserId) = LOWER(@UserId)
                    AND c.PhoneNumber IN @CustomerNumbers";

            var parameters = new
            {
                BusinessId = businessId.ToString().ToLower(),
                UserId = userId.ToLower(),
                CustomerNumbers = customerNumbers
            };

            return (await _unitOfWork.Connection.QueryAsync<Contacts>(contactQuery, parameters, _unitOfWork.Transaction)).ToList();
        }

        public async Task<IEnumerable<Conversations>> GetLatestConversationsAsync(Guid businessId, CancellationToken cancellationToken)
        {
            var conversationQuery = @"SELECT [From], [To], TextMessage, MediaMimeType, MediaFileName, TemplateBody, CreatedAt, Status
                               FROM Conversations
                               WHERE LOWER([From]) = LOWER(@BusinessId) OR LOWER([To]) = LOWER(@BusinessId)";

            var conversations = (await _unitOfWork.Connection.QueryAsync<Conversations>(conversationQuery, new { BusinessId = businessId.ToString().ToLower() },
                _unitOfWork.Transaction)).ToList();
            return conversations;
        }

        public async Task<IEnumerable<Contacts>> GetAllContactsAsync(Guid businessId, string userId, CancellationToken cancellationToken)
        {
            var roleQuery = @"SELECT r.Name 
                      FROM UserRole ur
                      JOIN Role r ON LOWER(ur.RoleId) = LOWER(r.Id)
                      WHERE LOWER(ur.Id) = LOWER(@UserId)";
            var role = await _unitOfWork.Connection.QueryFirstOrDefaultAsync<string>(roleQuery, new { UserId = userId }, _unitOfWork.Transaction);

            var contactQuery = role == "Admin" || role == "Owner"
                       ? @"SELECT *
                           FROM Contacts c
                           WHERE LOWER(c.BusinessId) = LOWER(@BusinessId) AND c.IsActive = 1"
                       : @"SELECT *
                           FROM Contacts c
                           WHERE LOWER(c.BusinessId) = LOWER(@BusinessId) AND c.IsActive = 1 AND LOWER(c.UserId) = LOWER(@UserId)";

            return (await _unitOfWork.Connection.QueryAsync<Contacts>(contactQuery, new { BusinessId = businessId.ToString().ToLower(), UserId = userId.ToLower() },
                _unitOfWork.Transaction)).ToList();
        }

        public async Task<IEnumerable<Conversations>> GetConversationAsync(string companyId, DateTime fromDate, DateTime toDate, CancellationToken cancellationToken)
        {
            try
            {
                var param = new
                {
                    CompanyId = companyId,
                    @FromDate = fromDate,
                    @ToDate = toDate
                };

                if (cancellationToken.IsCancellationRequested)
                    cancellationToken.ThrowIfCancellationRequested();

                return await _unitOfWork.Connection.QueryAsync<Conversations>(
                    "Conversations_Get",
                    param,
                    _unitOfWork.Transaction,
                    commandType: CommandType.StoredProcedure);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
        public async Task<IEnumerable<EngagetoEntities.Entities.Contacts>> GetContactsAsync(string companyId, CancellationToken cancellationToken)
        {
            if (cancellationToken.IsCancellationRequested)
                cancellationToken.ThrowIfCancellationRequested();

            var sqlQuery = @"SELECT *
                             FROM Contacts
                             WHERE BusinessId = @CompanyId
                             AND IsActive = @IsActive";
            var param = new
            {
                CompanyId = companyId,
                IsActive = true,
            };

            return await _unitOfWork.Connection.QueryAsync<EngagetoEntities.Entities.Contacts>(
                    sqlQuery,
                    param,
                    _unitOfWork.Transaction);
        }

        public async Task<IEnumerable<ChatStatusEntityDto>> GetChatStatusAsync(Guid businessId)
        {
            var param = new
            {
                BusinessId = businessId,
            };

            return await _unitOfWork.Connection.QueryAsync<ChatStatusEntityDto>(
                "ChatStatus_Get",
                param,
                _unitOfWork.Transaction,
                commandType: CommandType.StoredProcedure);
        }

        public async Task<int> UpdateOpenChatStatusAsync(Guid businessId)
        {
            var param = new
            {
                BusinessId = businessId,
            };

            return await _unitOfWork.Connection.ExecuteScalarAsync<int>(
                "OpenChatStatus_Update",
                param,
                _unitOfWork.Transaction,
                commandType: CommandType.StoredProcedure);
        }
        public async Task<IEnumerable<RespondedCountDto>> GetRespondedCountAsync(string companyId, DateTime fromDate, DateTime toDate, CancellationToken cancellationToken)
        {
            if (cancellationToken.IsCancellationRequested)
                cancellationToken.ThrowIfCancellationRequested();

            var sqlQuery = @"SELECT MIN([From]) [From],
                            [To],
                            COUNT(*) RespondedCount,
                            UserId
                            FROM Conversations
                            WHERE [From] =  @CompanyId
                            AND  CAST(CreatedAt as Date) >= @FromDate
                            AND CAST(CreatedAt as Date) <= @ToDate
                            Group by [To], UserId";

            var param = new
            {
                CompanyId = companyId,
                FromDate = fromDate,
                ToDate = toDate.AddDays(1),
            };

            return await _unitOfWork.Connection.QueryAsync<RespondedCountDto>(
                    sqlQuery,
                    param,
                    _unitOfWork.Transaction);
        }
        public async Task<IEnumerable<InboxAnalyticsTotalMessagsDto>> GetInboxTotalMessageCountAsync(List<EngagetoEntities.Entities.Contacts> contacts, string companyId,
          DateTime? fromDate,
          DateTime? toDate,
          CancellationToken cancellationToken)
        {
            if (cancellationToken.IsCancellationRequested)
                cancellationToken.ThrowIfCancellationRequested();

            // Create a list of phone numbers from the contacts
            var contactPhoneNumbers = contacts
                .Select(contact => $"{contact.CountryCode}{contact.Contact}".Replace("+", ""))
                .ToList();

            var sqlQuery = @"SELECT 
                    @CompanyId AS CompanyId,
                    SUM(
                        CASE  
                            WHEN [From] = @CompanyId AND [Status] IN (0,1,2) THEN 1
                            ELSE 0
                        END) AS SentCount,
                    SUM(
                        CASE  
                            WHEN [From] <> @CompanyId AND [Status] IN (0,1,2) THEN 1
                            ELSE 0
                        END) AS ReceivedCount,
                    SUM(CASE 
                            WHEN [Status] IN (5) THEN 1 
                            ELSE 0 
                        END) AS FaildCount
                FROM 
                    Conversations
                WHERE 
                    ([From] = @CompanyId OR [To] = @CompanyId)
                    AND ([From] IN @ContactPhoneNumbers OR [To] IN @ContactPhoneNumbers)
                    AND [Status] IN (0,1,2,5)
                    AND (@FromDate IS NULL OR CAST(CreatedAt AS DATE) >= @FromDate)
                    AND (@ToDate IS NULL OR CAST(CreatedAt AS DATE) <= @ToDate)";

            var param = new
            {
                CompanyId = companyId,
                ContactPhoneNumbers = contactPhoneNumbers,
                FromDate = fromDate,
                ToDate = toDate,
            };

            return await _unitOfWork.Connection.QueryAsync<InboxAnalyticsTotalMessagsDto>(
                    sqlQuery,
                    param,
                    _unitOfWork.Transaction);
        }


        public async Task<IEnumerable<TopUserPerformanceDto>> GetUserPerformanceAsync(List<EngagetoEntities.Entities.Contacts> contacts, string companyId,
       DateTime? fromDate,
       DateTime? toDate,
       CancellationToken cancellationToken)
        {
            if (cancellationToken.IsCancellationRequested)
                cancellationToken.ThrowIfCancellationRequested();

            // Create a list of phone numbers from the contacts
            var contactPhoneNumbers = contacts
                .Select(contact => $"{contact.CountryCode}{contact.Contact}".Replace("+", ""))
                .ToList();

            var sqlQuery = @"SELECT 
                        @CompanyId AS CompanyId,
                        cv.UserId,
                        MIN(us.[Name]) [Name],
                        COUNT(*) AS MessageCount
                    FROM Conversations cv
                    LEFT JOIN Users us ON
                        CAST(us.Id AS NVARCHAR(100)) = cv.UserId
                    WHERE ([From] = @CompanyId OR [To] = @CompanyId)
                        AND ([From] = @CompanyId AND UserId IS NOT NULL)
                        AND ([From] IN @ContactPhoneNumbers OR [To] IN @ContactPhoneNumbers)
                        AND (@FromDate IS NULL OR CAST(CreatedAt AS DATE) >= @FromDate)
                        AND (@ToDate IS NULL OR CAST(CreatedAt AS DATE) <= @ToDate)
                    GROUP BY UserId";

            var param = new
            {
                CompanyId = companyId,
                ContactPhoneNumbers = contactPhoneNumbers,
                FromDate = fromDate,
                ToDate = toDate,
            };

            return await _unitOfWork.Connection.QueryAsync<TopUserPerformanceDto>(
                    sqlQuery,
                    param,
                    _unitOfWork.Transaction);
        }

        public async Task<IEnumerable<ChatStatusEntityDto>> GetAllChatStatusAsync(string companyId, CancellationToken cancellationToken, bool? IsAgent = null)
        {
            if (cancellationToken.IsCancellationRequested)
                cancellationToken.ThrowIfCancellationRequested();
            string? sqlQuery = string.Empty;
            if (IsAgent == null)
            {
                sqlQuery = @"select cs.* 
                            FROM Contacts c
                            INNER JOIN ChatStatusEntities cs ON
	                            cs.ContactId = c.ContactId
                            Where c.BusinessId = @CompanyId";
            }
            else
            {
                sqlQuery = @"select c.* from
                            ChatStatusEntities as c inner join Users as u on c.userid=u.Id
                            Where u.CompanyId = @CompanyId";
            }


            var param = new
            {
                CompanyId = companyId
            };

            return await _unitOfWork.Connection.QueryAsync<ChatStatusEntityDto>(
                    sqlQuery,
                    param,
                    _unitOfWork.Transaction);
        }

        public async Task<IEnumerable<ContactInfoDto>> GetContactInfoAsync(Guid businessId,
             DateTime? fromDate,
             DateTime? toDate,
             CancellationToken cancellationToken)
        {
            if (cancellationToken.IsCancellationRequested)
                cancellationToken.ThrowIfCancellationRequested();

            var sqlQuery = @"SELECT 
                        CONCAT(REPLACE(c.CountryCode,'+',''), c.Contact) AS ContactInfo,
                        c.CountryCode,
                        c.Contact,
                        c.BusinessId,
                        c.ContactId,
                        c.[Name],
                        cs.[Status] AS Status,
                        cs.[Date],
                        cs.UserId
                     FROM Contacts c
                     OUTER APPLY 
                     (
                         SELECT TOP(1) 
                             cs.ContactId,
                             cs.[Status],
                             cs.[Date],
                             cs.UserId
                         FROM ChatStatusEntities cs
                         WHERE cs.ContactId = c.ContactId
                             AND (@FromDate IS NULL OR cs.[Date] >= @FromDate)
                             AND (@ToDate IS NULL OR cs.[Date] <= @ToDate)
                         ORDER BY cs.[Date] DESC
                     ) cs
                     WHERE c.BusinessId = @BusinessId";

            var param = new
            {
                BusinessId = businessId,
                FromDate = fromDate,
                ToDate = toDate,
            };

            return await _unitOfWork.Connection.QueryAsync<ContactInfoDto>(
                sqlQuery,
                param,
                _unitOfWork.Transaction);
        }

        public async Task<IEnumerable<AgentAvgResponseTimeDto>> GetAgentAvgResponseTime(string businessId, DateTime date, CancellationToken cancellationToken)
        {
            var param = new
            {
                CompanyId = businessId,
                @Date = date
            };
            if (cancellationToken.IsCancellationRequested)
                cancellationToken.ThrowIfCancellationRequested();

            return await _unitOfWork.Connection.QueryAsync<AgentAvgResponseTimeDto>(
                "AgentAvgResponseTime_Get",
                param,
                _unitOfWork.Transaction,
                commandType: CommandType.StoredProcedure);
        }
        public async Task<IEnumerable<T>> GetContactAssignmentsAsync<T>(Guid? companyId, Guid? contactId)
        {
            try
            {
                var param = new
                {
                    CompanyId = companyId,
                    ContactId = contactId,
                };

                return await _unitOfWork.Connection.QueryAsync<T>(
                    "LatestContactAssignmentHistories_Get",
                    param,
                    _unitOfWork.Transaction,
                    commandType: CommandType.StoredProcedure);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<IEnumerable<T>> GetContactAssignmentHistoriesAsync<T>(Guid? companyId, Guid? contactId, DateTime fromDate, DateTime toDate, CancellationToken cancellationToken)
        {
            try
            {
                if (cancellationToken.IsCancellationRequested)
                    cancellationToken.ThrowIfCancellationRequested();

                var param = new
                {
                    CompanyId = companyId,
                    ContactId = contactId,
                    FromDate = fromDate,
                    ToDate = toDate,
                };

                return await _unitOfWork.Connection.QueryAsync<T>(
                    "ContactAssignmentHistories_Get",
                    param,
                    _unitOfWork.Transaction,
                    commandType: CommandType.StoredProcedure);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<bool> CheckConversationStatus(string companyId, string phoneNumber, CancellationToken cancellationToken)
        {
            try
            {
                if (cancellationToken.IsCancellationRequested)
                    cancellationToken.ThrowIfCancellationRequested();

                // Define SQL query to call the scalar function
                string sql = @"
                    SELECT dbo.CheckConversationStatus(@CompanyId, @PhoneNumber)";

                var parameters = new
                {
                    CompanyId = companyId,
                    PhoneNumber = phoneNumber
                };

                // Execute the query and retrieve the result
                var result = await _unitOfWork.Connection.QueryFirstOrDefaultAsync<int>(
                    sql,
                    parameters,
                    _unitOfWork.Transaction,
                    commandType: CommandType.Text);

                // Convert result to boolean
                return result == 1;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in CheckConversationStatus: {ex.Message}", ex);
                return true;
            }
        }


        public async Task<IEnumerable<ContactInfoDto>> GetContactDetailsByIds(string businessId, List<Guid> contactIds, CancellationToken cancellationToken)
        {
            try
            {
                if (cancellationToken.IsCancellationRequested)
                    cancellationToken.ThrowIfCancellationRequested();

                const int maxParamsPerQuery = 1000;
                var result = new List<ContactInfoDto>();

                var batches = contactIds
                    .Where(id => id != Guid.Empty)
                    .Select((id, index) => new { id, index })
                    .GroupBy(x => x.index / maxParamsPerQuery)
                    .Select(g => g.Select(x => x.id).ToList());
               
                foreach (var batch in batches)
                {
                    var sqlQuery = @"
                        SELECT ContactId,
                               BusinessId,
                               Contact,
                               CountryCode,
                               Name,
                               Email,
                               CountryName,
                               Contact ContactNumber
                        FROM Contacts
                        WHERE BusinessId = @CompanyId
                          AND IsDeleted = @IsDeleted
                          AND ContactId IN @ContactIds";

                    var param = new DynamicParameters();
                    param.Add("CompanyId", businessId);
                    param.Add("IsDeleted", false);
                    param.Add("ContactIds", batch.ToArray());
                    using var connection = _connectionFactory.CreateConnection();
                    var batchResult = await connection.QueryAsync<ContactInfoDto>(
                        sqlQuery,
                        param,
                        _unitOfWork.Transaction);

                    result.AddRange(batchResult);
                }

                return result;
            }
            catch (Exception ex)
            {
                throw; // Consider logging the exception here
            }
        }


        public async Task<Dictionary<Guid, IEnumerable<CampaignAnalytsicDto>>> GetCampaignAnalyticsCountAsync(string businessId, Dictionary<Guid, List<string>> values, CancellationToken cancellationToken)
        {
            try
            {
                if (cancellationToken.IsCancellationRequested)
                    cancellationToken.ThrowIfCancellationRequested();

                // Prepare data as JSON
                var waIdList = values
                    .SelectMany(kv => kv.Value.Select(waId => new { CampaignId = kv.Key, WaId = waId }))
                    .ToList();
                var waIdJson = JsonConvert.SerializeObject(waIdList);


                var param = new DynamicParameters();
                param.Add("CompanyId", businessId);
                param.Add("WaIdsJson", waIdJson);

                // Execute Query
                var response = await _unitOfWork.Connection.QueryAsync<CampaignAnalytsicDto>(
                        "CampaignMessageStats_Get",
                        param,
                        _unitOfWork.Transaction,
                        commandType: CommandType.StoredProcedure);

                // Transform Results
                return response.GroupBy(x => x.CampaignId).ToDictionary(g => g.Key, g => g.AsEnumerable());
            }
            catch (Exception ex)
            {
                // Log exception
                throw;
            }
        }

        public async Task<bool> UpdateContactForWorkflowAsync(string businessId, List<Guid> contactIds, string workflowName, int step, CancellationToken cancellationToken)
        {
            try
            {
                if (cancellationToken.IsCancellationRequested)
                    cancellationToken.ThrowIfCancellationRequested();

                var sqlQuery = @"
                    UPDATE Contacts 
                    SET 
                        WorkflowName = @WorkflowName,
                        WorkflowStep = @WorkflowStep
                    WHERE 
                        BusinessId = @BusinessId
                        AND IsActive = @IsActive
                        AND ContactId IN @ContactIds";

                var param = new DynamicParameters();
                param.Add("WorkflowName", workflowName);
                param.Add("WorkflowStep", step);
                param.Add("BusinessId", businessId);
                param.Add("IsActive", true);
                param.Add("ContactIds", contactIds.Select(i => i.ToString()).ToArray());

                var rowsAffected = await _unitOfWork.Connection.ExecuteAsync(
                    sqlQuery,
                    param,
                    _unitOfWork.Transaction);

                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<TenantCCostAnalyticsDto?> GetCConversationCostAnalyticsAsync(string businessId, DateTime fromDate, DateTime toDate, CancellationToken cancellationToken)
        {
            try
            {
                if (cancellationToken.IsCancellationRequested)
                    cancellationToken.ThrowIfCancellationRequested();

                var sqlQuery = @"
                    SELECT 
				        SUM(CASE WHEN ConversationType = 'FREE_TIER' THEN [Conversation] ELSE 0 END) AS FreeConversation,
				        SUM(CASE WHEN ConversationType = 'REGULAR' THEN [Conversation] ELSE 0 END) AS PaidConversation,
				        SUM(CASE WHEN ConversationType = 'REGULAR' THEN Cost ELSE 0 END) AS Cost,
				        SUM(CASE WHEN ConversationCategory = 'MARKETING' THEN [Conversation] ELSE 0 END) AS MarketingConversation,
				        SUM(CASE WHEN ConversationCategory = 'UTILITY' THEN [Conversation] ELSE 0 END) AS UtilityConversation,
                        SUM(CASE WHEN ConversationCategory = 'AUTHENTICATION' THEN [Conversation] ELSE 0 END) AS AuthenticationConversation
			        FROM ConversationAnalyticsEntities
			        WHERE (@FromDate IS NULL OR CAST(StartDate AS DATE) >= @FromDate)
			        AND (@ToDate IS NULL OR CAST(StartDate AS DATE) <= @ToDate)
			        AND CompanyId = @CompanyId";

                var param = new DynamicParameters();
                param.Add("FromDate", fromDate);
                param.Add("ToDate", toDate);
                param.Add("CompanyId", businessId);

                var result = await _unitOfWork.Connection.QueryAsync<TenantCCostAnalyticsDto>(sqlQuery,
                    param,
                    _unitOfWork.Transaction);

                return result.FirstOrDefault();
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<TemplateAnalyticsDto?> GetTemplateAnalyticsAsync(string businessId, DateTime fromDate, DateTime toDate, CancellationToken cancellationToken)
        {
            try
            {
                if (cancellationToken.IsCancellationRequested)
                    cancellationToken.ThrowIfCancellationRequested();

                var sqlQuery = @"
                    SELECT 
				        SUM(CASE WHEN [Status] = 1 THEN 1 ELSE 0 END) AS PendingTemplate,
				        SUM(CASE WHEN [Status] = 2 THEN 1 ELSE 0 END) AS ApprovedTemplate,
				        SUM(CASE WHEN [Status] = 3 THEN 1 ELSE 0 END) AS RejectedTemplate,
				        SUM(CASE WHEN Category = 1 THEN 1 ELSE 0 END) AS MarketingTemplate,
				        SUM(CASE WHEN Category = 2 THEN 1 ELSE 0 END) AS UtlityTemplate,
				        SUM(CASE WHEN Category = 3 THEN 1 ELSE 0 END) AS AuthenticationTemplate
			        FROM Templates
			        WHERE 
				    (@FromDate IS NULL OR CAST(CreatedDate AS DATE) >= @FromDate)
				    AND (@ToDate IS NULL OR CAST(CreatedDate AS DATE) <= @ToDate)
				    AND BusinessId = @CompanyId";

                var param = new DynamicParameters();
                param.Add("FromDate", fromDate);
                param.Add("ToDate", toDate);
                param.Add("CompanyId", businessId);

                var result = await _unitOfWork.Connection.QueryAsync<TemplateAnalyticsDto>(sqlQuery,
                    param,
                    _unitOfWork.Transaction);

                return result.FirstOrDefault();
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<int> SendTemplateCountAsync(string businessId, DateTime fromDate, DateTime toDate, CancellationToken cancellationToken)
        {
            try
            {
                if (cancellationToken.IsCancellationRequested)
                    cancellationToken.ThrowIfCancellationRequested();

                var sqlQuery = @"
			        SELECT COUNT(*) SentTemplate
			        FROM Conversations c
			        LEFT JOIN Campaigns cam ON
				        c.ReferenceId = CAST(cam.TemplateId AS NVARCHAR(100))
				        and (cam.TemplateId IS NOT NULL OR cam.TemplateId != '00000000-0000-0000-0000-000000000000')
			        WHERE 
				        (@FromDate IS NULL OR CAST(CreatedAt AS DATE) >= @FromDate)
				        AND (@ToDate IS NULL OR CAST(CreatedAt AS DATE) <= @ToDate)
				        AND ([From] = @CompanyId OR [To] = @CompanyId)
				        And  MessageType IN (2,3)
				        AND c.ReferenceId IS NOT NULL";

                var param = new DynamicParameters();
                param.Add("FromDate", fromDate);
                param.Add("ToDate", toDate);
                param.Add("CompanyId", businessId);

                var result = await _unitOfWork.Connection.ExecuteScalarAsync<int>(sqlQuery,
                    param,
                    _unitOfWork.Transaction);

                return result;
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        public async Task<WATemplateCategory> GetTemplateCategoryAsync(string referenceId)
        {
            try
            {
                string query = @"
                        WITH Template AS
                        (
                            SELECT Category 
                            FROM Templates 
                            WHERE TemplateId = @ReferenceId

                            UNION 

                            SELECT t.Category
                            FROM Campaigns c
                            INNER JOIN Templates t ON
                                c.TemplateId = t.TemplateId
                            WHERE CampaignId = @ReferenceId
                            AND c.TemplateId IS NOT NULL 
                            AND c.TemplateId != '00000000-0000-0000-0000-000000000000'
                        )
                        SELECT TOP 1 Category FROM Template";

                var result = await _unitOfWork.Connection.QueryFirstOrDefaultAsync<int?>(
                    query,
                    new { ReferenceId = referenceId });

                if (result.HasValue && Enum.IsDefined(typeof(WATemplateCategory), result.Value))
                {
                    WATemplateCategory category = (WATemplateCategory)result.Value;
                    return category;
                }
                else
                {
                    return WATemplateCategory.None;
                }

            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<bool> SaveConversationCostAsync(ConversationAnalyticsEntity conversation)
        {
            try
            {

                var sql = "[dbo].[ConversationAnalytics_Save]";
                var parameters = new DynamicParameters();

                parameters.Add("@WAMessageId", conversation.WAMessageId);
                parameters.Add("@CompanyId", conversation.CompanyId);
                parameters.Add("@Conversation", conversation.Conversation);
                parameters.Add("@ConversationCategory", conversation.ConversationCategory?.ToString());
                parameters.Add("@ConversationType", conversation.ConversationType);
                parameters.Add("@Cost", conversation.Cost);
                parameters.Add("@CreatedAt", conversation.CreatedAt);
                parameters.Add("@UpdatedAt", conversation.UpdatedAt);
                parameters.Add("@StartDate", conversation.StartDate);
                parameters.Add("@EndDate", conversation.EndDate);
                parameters.Add("@MobileNumber", conversation.MobileNumber);
                parameters.Add("@CurrentBalance", conversation.CurrentBalance);
                parameters.Add("@Country", conversation.Country);

                // 👇 Define the OUTPUT parameter
                parameters.Add("@Result", dbType: DbType.Int32, direction: ParameterDirection.Output);
                using var connection = _connectionFactory.CreateConnection();
                // Execute the stored procedure
                await connection.ExecuteAsync(
                    sql,
                    parameters,
                    commandType: CommandType.StoredProcedure);

                // 👇 Get the result from output param
                int result = parameters.Get<int>("@Result");
                return result == 1;
            }
            catch (Exception ex)
            {
                // Log the exception (adjust logging as needed for your application)
                Console.WriteLine($"Error: {ex.Message}");
                return false;
            }
        }

        public async Task<CampaignAnalytsicDto> GetCampaignAnalyticsCountAsyncById(string businessId, Guid campaignId, CancellationToken cancellationToken)
        {
            try
            {
                var param = new DynamicParameters();
                param.Add("CompanyId", businessId);
                param.Add("CampaignId", campaignId);

                var response = await _unitOfWork.Connection.QueryFirstOrDefaultAsync<CampaignAnalytsicDto>(
               "CampaignMessageStats_Staus",
               param,
               _unitOfWork.Transaction,
               commandType: CommandType.StoredProcedure);
                return response;
            }
            catch (Exception ex)
            {
                throw;
            }

        }

        public async Task<Conversations> UpdateConversatonAndCampaignTrackerAsync(
            string waMessageId,
            ConvStatus status,
            string? errorMessage = null,
            string? errorDetails = null)
        {
            try
            {
                var parameters = new DynamicParameters();
                parameters.Add("@WhatsAppMessageId", waMessageId);
                parameters.Add("@Status", (int)status); // assuming enum mapped as int
                parameters.Add("@ErrorMessage", errorMessage);
                parameters.Add("@ErrorDetails", errorDetails);

                using var connection = _connectionFactory.CreateConnection();
                var conversation = await connection.QueryFirstAsync<Conversations>(
                    "ConversationAndTracker_Update",
                    parameters,
                    commandType: CommandType.StoredProcedure);

                return conversation;
            }
            catch (Exception ex)
            {
                //_logger.LogError(ex, "Error updating conversation and campaign tracker for WhatsAppMessageId: {MessageId}", waMessageId);
                throw;
            }
        }

        public async Task<bool> IsStopAutoReplyMessageAsync(
            string businessId,
            string phoneNumber,
            bool isStopAutoReplyMessageForCampaign,
            int? second = 10,
            CancellationToken cancellationToken = default)
        {
            try
            {
                const string baseQuery = @"
                    SELECT COUNT(*) 
                    FROM Conversations 
                    WHERE [From] = @BusinessId 
                    AND REPLACE([To], '+', '') = @PhoneNumber
                    AND DATEDIFF(SECOND, CreatedAt, GETUTCDATE()) < @Second";

                string sqlQuery = baseQuery;

                if (isStopAutoReplyMessageForCampaign)
                {
                    sqlQuery += " AND ReferenceId IS NOT NULL";
                }

                using (var connection = _connectionFactory.CreateConnection())
                {
                    var parameters = new DynamicParameters();
                    parameters.Add("@BusinessId", businessId);
                    parameters.Add("@PhoneNumber", phoneNumber);
                    parameters.Add("@Second", second ?? 10);

                    var count = await connection.ExecuteScalarAsync<int>(
                        new CommandDefinition(sqlQuery, parameters, cancellationToken: cancellationToken));

                    //_logger.LogInformation("IsStopAutoReplyMessageAsync executed. BusinessId: {BusinessId}, PhoneNumber: {PhoneNumber}, Count: {Count}, TimeWindowSeconds: {Second}, IsCampaignCheck: {IsCampaign}",
                    // businessId, phoneNumber, count, second, isStopAutoReplyMessageForCampaign);

                    return count > 0;
                }
            }
            catch (Exception ex)
            {
                //_logger.LogError(ex, "Error occurred in IsStopAutoReplyMessageAsync. BusinessId: {BusinessId}, PhoneNumber: {PhoneNumber}, TimeWindowSeconds: {Second}, IsCampaignCheck: {IsCampaign}",
                //businessId, phoneNumber, second, isStopAutoReplyMessageForCampaign);
                throw;
            }
        }
    }
}

<?xml version="1.0" encoding="utf-8"?>
<!--
This file is used by the publish/package process of your Web project. You can customize the behavior of this process
by editing this MSBuild file. In order to learn more about this please visit https://go.microsoft.com/fwlink/?LinkID=208121. 
-->
<Project>
  <PropertyGroup>
    <WebPublishMethod>Custom</WebPublishMethod>
    <SCMUrl>https://dev-enageto-web.scm.azurewebsites.net/</SCMUrl>
    <ContainerResourceId>/subscriptions/************************************/resourceGroups/az-lrb-common/providers/Microsoft.ContainerRegistry/registries/lrbcontainer</ContainerResourceId>
    <ContainerResourceName>lrbcontainer</ContainerResourceName>
    <Subscription>Microsoft Azure Sponsorship</Subscription>
    <Subtype>ContainerRegistry</Subtype>
    <RegistryUrl>lrbcontainer.azurecr.io</RegistryUrl>
    <DockerPublish>true</DockerPublish>
    <ResourceId>/subscriptions/************************************/resourceGroups/az-lrb-dev/providers/Microsoft.Web/sites/dev-enageto-web</ResourceId>
    <ResourceGroup>az-lrb-dev</ResourceGroup>
    <LaunchSiteAfterPublish>true</LaunchSiteAfterPublish>
    <SiteUrlToLaunchAfterPublish>https://dev-enageto-web.azurewebsites.net</SiteUrlToLaunchAfterPublish>
    <PublishProvider>AppServiceContainer</PublishProvider>
    <LastUsedBuildConfiguration>Release</LastUsedBuildConfiguration>
    <LastUsedPlatform>Any CPU</LastUsedPlatform>
    <ProjectGuid>bb2a877a-7cfb-4085-bf03-3ebb2fc47247</ProjectGuid>
  </PropertyGroup>
</Project>